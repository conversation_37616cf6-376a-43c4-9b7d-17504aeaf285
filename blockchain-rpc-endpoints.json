[{"name": "Ethereum Mainnet", "chainId": 1, "url": ["https://mainnet.infura.io/v3/", "https://eth.llamarpc.com", "https://ethereum-rpc.publicnode.com", "https://rpc.mevblocker.io/noreverts", "https://rpc.eth.gateway.fm", "https://rpc.flashbots.net", "https://core.gashawk.io/rpc", "https://rpc.flashbots.net", "https://mainnet.gateway.tenderly.co", "https://eth.nodeconnect.org", "https://rpc.flashbots.net", "https://mainnet.optimism.io", "https://rpc-mainnet.kcc.network", "https://rpc.flashbots.net", "https://eth-mainnet.public.blastapi.io", "https://bsc-dataseed.binance.org", "https://bsc-dataseed1.binance.org", "https://bsc-dataseed1.defibit.io", "https://bsc-dataseed.binance.org", "https://bsc-dataseed1.ninicoin.io", "https://rpc-mainnet.matic.quiknode.pro", "https://mainnet.optimism.io", "https://arb1.arbitrum.io/rpc", "https://api.avax.network/ext/bc/C/rpc", "https://rpcapi.fantom.network", "https://rpc.moonriver.moonbeam.network", "https://rpc.gnosischain.com", "https://api.harmony.one", "https://forno.celo.org", "https://mainnet.aurora.dev", "https://rpc.fuse.io", "https://evm-cronos.crypto.org", "https://evmexplorer.velas.com/rpc", "https://rpc.meter.io", "https://rpc.api.moonbeam.network", "https://andromeda.metis.io/?owner=1088", "https://rpc.c1.milkomeda.com:8545", "https://mainnet.telos.net/evm", "https://exchainrpc.okex.org", "https://rpc.astar.network", "https://emerald.oasis.dev", "https://eth-rpc-api.thetatoken.org/rpc", "https://gwan-ssl.wandevs.org:56891", "https://rpc.bittorrentchain.io", "https://rpctest.meter.io", "https://eth-rpc-api-testnet.thetatoken.org/rpc", "https://rpc.shiden.astar.network", "https://rpc.api.moonbeam.network", "https://evm-cronos.crypto.org", "https://mainnet.aurora.dev", "https://babel-api.mainnet.iotex.io", "https://rpc.meter.io", "https://rpc.syscoin.org", "https://rpc.api.moonbeam.network", "https://rpc-mainnet.findora.org", "https://eth-rpc-karura.aca-api.network", "https://mainnet.boba.network", "https://api.harmony.one", "https://rpc.fuse.io", "https://forno.celo.org", "https://mainnet-rpc.thundercore.com", "https://api.harmony.one", "https://evm.confluxrpc.com"]}, {"name": "Optimism", "chainId": 10, "url": ["https://mainnet.optimism.io", "https://optimism-mainnet.public.blastapi.io", "https://rpc.optimism.gateway.fm"]}, {"name": "Cronos Mainnet Beta", "chainId": 25, "url": ["https://evm.cronos.org", "https://evm-cronos.crypto.org"]}, {"name": "BNB Smart Chain Mainnet", "chainId": 56, "url": ["https://bsc-dataseed.binance.org", "https://bsc-dataseed1.binance.org", "https://bsc-dataseed2.binance.org", "https://bsc-dataseed3.binance.org", "https://bsc-dataseed4.binance.org", "https://bsc-dataseed1.defibit.io", "https://bsc-dataseed2.defibit.io", "https://bsc-dataseed1.ninicoin.io"]}, {"name": "Gnosis", "chainId": 100, "url": ["https://rpc.gnosischain.com", "https://rpc.gnosis.gateway.fm"]}, {"name": "<PERSON><PERSON>", "chainId": 122, "url": ["https://rpc.fuse.io"]}, {"name": "Polygon Mainnet", "chainId": 137, "url": ["https://polygon-rpc.com", "https://rpc-mainnet.matic.network", "https://matic-mainnet.chainstacklabs.com", "https://rpc-mainnet.maticvigil.com", "https://rpc-mainnet.matic.quiknode.pro", "https://matic-mainnet-full-rpc.bwarelabs.com"]}, {"name": "Fantom Opera", "chainId": 250, "url": ["https://rpcapi.fantom.network", "https://rpc.ftm.tools", "https://rpc.fantom.network"]}, {"name": "Moonbeam", "chainId": 1284, "url": ["https://rpc.api.moonbeam.network", "https://rpc.ankr.com/moonbeam"]}, {"name": "Moonriver", "chainId": 1285, "url": ["https://rpc.api.moonriver.moonbeam.network", "https://rpc.moonriver.moonbeam.network"]}, {"name": "Arbitrum One", "chainId": 42161, "url": ["https://arb1.arbitrum.io/rpc", "https://arbitrum-mainnet.infura.io/v3/", "https://rpc.arb1.arbitrum.gateway.fm"]}, {"name": "Celo <PERSON>net", "chainId": 42220, "url": ["https://forno.celo.org"]}, {"name": "Avalanche C-Chain", "chainId": 43114, "url": ["https://api.avax.network/ext/bc/C/rpc", "https://rpc.ankr.com/avalanche", "https://ava-mainnet.public.blastapi.io/ext/bc/C/rpc"]}, {"name": "Aurora Mainnet", "chainId": 1313161554, "url": ["https://mainnet.aurora.dev"]}, {"name": "Harmony Mainnet Shard 0", "chainId": 1666600000, "url": ["https://api.harmony.one", "https://api.s0.t.hmny.io"]}]