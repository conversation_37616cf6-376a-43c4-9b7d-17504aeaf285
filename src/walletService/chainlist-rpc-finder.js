import * as fs from "fs";
import * as https from "https";
//only main net list
async function generateBlockchainRPCJson() {
  const predefinedRPC = [
    {
      name: "Ethereum Mainnet",
      chainId: 1,
      url: [
        `https://mainnet.infura.io/v3/`,
        "https://eth.llamarpc.com",
        "https://ethereum-rpc.publicnode.com",
        "https://rpc.mevblocker.io/noreverts",
        "https://rpc.eth.gateway.fm",
        "https://rpc.flashbots.net",
        "https://core.gashawk.io/rpc",
        "https://rpc.flashbots.net",
        "https://mainnet.gateway.tenderly.co",
        "https://eth.nodeconnect.org",
        "https://rpc.flashbots.net",
        "https://mainnet.optimism.io",
        "https://rpc-mainnet.kcc.network",
        "https://rpc.flashbots.net",
        "https://eth-mainnet.public.blastapi.io",
        "https://bsc-dataseed.binance.org",
        "https://bsc-dataseed1.binance.org",
        "https://bsc-dataseed1.defibit.io",
        "https://bsc-dataseed.binance.org",
        "https://bsc-dataseed1.ninicoin.io",
        "https://rpc-mainnet.matic.quiknode.pro",
        "https://mainnet.optimism.io",
        "https://arb1.arbitrum.io/rpc",
        "https://api.avax.network/ext/bc/C/rpc",
        "https://rpcapi.fantom.network",
        "https://rpc.moonriver.moonbeam.network",
        "https://rpc.gnosischain.com",
        "https://api.harmony.one",
        "https://forno.celo.org",
        "https://mainnet.aurora.dev",
        "https://rpc.fuse.io",
        "https://evm-cronos.crypto.org",
        "https://evmexplorer.velas.com/rpc",
        "https://rpc.meter.io",
        "https://rpc.api.moonbeam.network",
        "https://andromeda.metis.io/?owner=1088",
        "https://rpc.c1.milkomeda.com:8545",
        "https://mainnet.telos.net/evm",
        "https://exchainrpc.okex.org",
        "https://rpc.astar.network",
        "https://emerald.oasis.dev",
        "https://eth-rpc-api.thetatoken.org/rpc",
        "https://gwan-ssl.wandevs.org:56891",
        "https://rpc.bittorrentchain.io",
        "https://rpctest.meter.io",
        "https://eth-rpc-api-testnet.thetatoken.org/rpc",
        "https://rpc.shiden.astar.network",
        "https://rpc.api.moonbeam.network",
        "https://evm-cronos.crypto.org",
        "https://mainnet.aurora.dev",
        "https://babel-api.mainnet.iotex.io",
        "https://rpc.meter.io",
        "https://rpc.syscoin.org",
        "https://rpc.api.moonbeam.network",
        "https://rpc-mainnet.findora.org",
        "https://eth-rpc-karura.aca-api.network",
        "https://mainnet.boba.network",
        "https://api.harmony.one",
        "https://rpc.fuse.io",
        "https://forno.celo.org",
        "https://mainnet-rpc.thundercore.com",
        "https://api.harmony.one",
        "https://evm.confluxrpc.com",
      ],
    },
    {
      name: "BNB Smart Chain Mainnet",
      chainId: 56,
      url: [
        "https://bsc-dataseed.binance.org",
        "https://bsc-dataseed1.binance.org",
        "https://bsc-dataseed2.binance.org",
        "https://bsc-dataseed3.binance.org",
        "https://bsc-dataseed4.binance.org",
        "https://bsc-dataseed1.defibit.io",
        "https://bsc-dataseed2.defibit.io",
        "https://bsc-dataseed1.ninicoin.io",
      ],
    },
    {
      name: "Polygon Mainnet",
      chainId: 137,
      url: [
        "https://polygon-rpc.com",
        "https://rpc-mainnet.matic.network",
        "https://matic-mainnet.chainstacklabs.com",
        "https://rpc-mainnet.maticvigil.com",
        "https://rpc-mainnet.matic.quiknode.pro",
        "https://matic-mainnet-full-rpc.bwarelabs.com",
      ],
    },
    {
      name: "Arbitrum One",
      chainId: 42161,
      url: [
        "https://arb1.arbitrum.io/rpc",
        "https://arbitrum-mainnet.infura.io/v3/",
        "https://rpc.arb1.arbitrum.gateway.fm",
      ],
    },
    {
      name: "Optimism",
      chainId: 10,
      url: [
        "https://mainnet.optimism.io",
        "https://optimism-mainnet.public.blastapi.io",
        "https://rpc.optimism.gateway.fm",
      ],
    },
    {
      name: "Avalanche C-Chain",
      chainId: 43114,
      url: [
        "https://api.avax.network/ext/bc/C/rpc",
        "https://rpc.ankr.com/avalanche",
        "https://ava-mainnet.public.blastapi.io/ext/bc/C/rpc",
      ],
    },
    {
      name: "Fantom Opera",
      chainId: 250,
      url: [
        "https://rpcapi.fantom.network",
        "https://rpc.ftm.tools",
        "https://rpc.fantom.network",
      ],
    },
    {
      name: "Cronos Mainnet Beta",
      chainId: 25,
      url: ["https://evm.cronos.org", "https://evm-cronos.crypto.org"],
    },
    {
      name: "Moonbeam",
      chainId: 1284,
      url: [
        "https://rpc.api.moonbeam.network",
        "https://rpc.ankr.com/moonbeam",
      ],
    },
    {
      name: "Moonriver",
      chainId: 1285,
      url: [
        "https://rpc.api.moonriver.moonbeam.network",
        "https://rpc.moonriver.moonbeam.network",
      ],
    },
    {
      name: "Harmony Mainnet Shard 0",
      chainId: 1666600000,
      url: ["https://api.harmony.one", "https://api.s0.t.hmny.io"],
    },
    {
      name: "Aurora Mainnet",
      chainId: 1313161554,
      url: ["https://mainnet.aurora.dev"],
    },
    {
      name: "Fuse Mainnet",
      chainId: 122,
      url: ["https://rpc.fuse.io"],
    },
    {
      name: "Gnosis",
      chainId: 100,
      url: ["https://rpc.gnosischain.com", "https://rpc.gnosis.gateway.fm"],
    },
    {
      name: "Celo Mainnet",
      chainId: 42220,
      url: ["https://forno.celo.org"],
    },
  ];

  try {
    console.log("Fetching blockchain data from chainid.network...");

    const data = await fetchChainData();

    // Filter for mainnet only - exclude testnets and devnets
    const isMainnet = (name) => {
      const lowerName = name.toLowerCase();
      const testnetKeywords = [
        "test",
        "testnet",
        "goerli",
        "sepolia",
        "ropsten",
        "kovan",
        "rinkeby",
        "mumbai",
        "chapel",
        "fuji",
        "arbitrum-goerli",
        "arbitrum-sepolia",
        "optimism-goerli",
        "optimism-sepolia",
        "polygon-mumbai",
        "bsc-testnet",
        "avalanche-fuji",
        "fantom-testnet",
        "cronos-testnet",
        "moonbase",
        "shibuya",
        "development",
        "devnet",
        "staging",
        "preview",
        "beta-testnet",
        "testnet-",
        "-testnet",
        "sandbox",
        "demo",
        "experimental",
      ];
      return !testnetKeywords.some((keyword) => lowerName.includes(keyword));
    };

    // Process additional chains from API (mainnet only)
    const additionalChains = data
      .filter(
        (chain) =>
          chain.rpc &&
          chain.rpc.length > 0 &&
          isMainnet(chain.name) &&
          !predefinedRPC.find((existing) => existing.chainId === chain.chainId)
      )
      .map((chain) => ({
        name: chain.name,
        chainId: chain.chainId,
        url: chain.rpc.filter(
          (rpcUrl) =>
            rpcUrl &&
            typeof rpcUrl === "string" &&
            !rpcUrl.includes("${") &&
            rpcUrl.startsWith("http")
        ),
      }))
      .filter((chain) => chain.url.length > 0);

    // Combine and sort all chains
    const allChains = [...predefinedRPC, ...additionalChains].sort(
      (a, b) => a.chainId - b.chainId
    );

    // Generate filename with current date
    const filename = `blockchain-rpc-endpoints.json`;

    // Write to JSON file
    fs.writeFileSync(filename, JSON.stringify(allChains, null, 2));

    console.log(`✅ Successfully generated ${filename} (MAINNET ONLY)`);
    console.log(`📊 Total mainnet blockchains: ${allChains.length}`);
    console.log(
      `🔗 Total mainnet RPC endpoints: ${allChains.reduce(
        (sum, chain) => sum + chain.url.length,
        0
      )}`
    );

    return allChains;
  } catch (error) {
    console.error("❌ Error generating blockchain RPC file:", error.message);
    throw error;
  }
}

function fetchChainData() {
  return new Promise((resolve, reject) => {
    const url = "https://chainid.network/chains.json";

    https
      .get(url, (res) => {
        let data = "";

        res.on("data", (chunk) => {
          data += chunk;
        });

        res.on("end", () => {
          try {
            const jsonData = JSON.parse(data);
            resolve(jsonData);
          } catch (error) {
            reject(new Error("Failed to parse JSON response"));
          }
        });
      })
      .on("error", (error) => {
        reject(new Error(`Failed to fetch data: ${error.message}`));
      });
  });
}

// Execute the function
generateBlockchainRPCJson()
  .then(() => {
    console.log("🎉 Blockchain RPC JSON file generated successfully!");
  })
  .catch((error) => {
    console.error("Failed to generate file:", error);
    process.exit(1);
  });
