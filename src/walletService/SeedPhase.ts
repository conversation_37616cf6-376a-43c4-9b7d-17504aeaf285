const bip39 = require("bip39");
const hdkey = require("hdkey");
const ethWallet = require("ethereumjs-wallet").default;

/**
 * Generates an Ethereum wallet using a BIP39 mnemonic seed phrase,
 * and derives 10 private keys and addresses from it.
 *
 * @returns {{ mnemonic: string, wallets: { index: number, privateKey: string, address: string }[] }}
 */
export function generateMultipleWalletsFromSeed() {
  // Step 1: Generate 12-word mnemonic
  const mnemonic = bip39.generateMnemonic();

  // Step 2: Convert mnemonic to seed
  const seed = bip39.mnemonicToSeedSync(mnemonic);

  // Step 3: Create HD root key from seed
  const root = hdkey.fromMasterSeed(seed);

  // Step 4: Derive multiple wallets from m/44'/60'/0'/0/i
  const wallets = [];

  for (let i = 0; i < 10; i++) {
    const addrNode = root.derive(`m/44'/60'/0'/0/${i}`);
    const wallet = ethWallet.fromPrivateKey(addrNode.privateKey);
    const privateKey = wallet.getPrivateKey().toString("hex");
    const address = wallet.getAddress().toString("hex");

    wallets.push({
      index: i,
      privateKey,
      address: `0x${address}`, // Lowercase '0x' is standard
    });
  }
  // console.log("--", mnemonic, wallets);
  return { mnemonic, wallets };
}
