#!/bin/bash

# Stop any container using port 1234
echo "Checking for processes using port 1234..."
PORT_PID=$(lsof -i :1234 -t)

if [ ! -z "$PORT_PID" ]; then
  echo "Stopping process using port 1234..."
  kill $(lsof -t -i:1234)
  kill -9 $PORT_PID || true
  sudo kill -9 1234
fi

# Check for any existing api container and stop it
if [ "$(docker ps -q -f name=api)" ]; then
  echo "Stopping existing API container..."
  docker stop api || true
  docker rm api || true
fi

# Build and start the docker containers in detached mode
echo "Starting Docker containers..."
docker-compose down || true
docker-compose build
docker-compose up -d

echo "API service started on port 1234"